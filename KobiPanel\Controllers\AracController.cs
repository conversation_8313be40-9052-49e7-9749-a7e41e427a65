﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.Vehicles;

namespace KobiPanel.Controllers
{

    public class AracController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Arac
        public ActionResult AracAnasayfa()
        {
            var araclar = db.Araclar.Include(a => a.Yakitlar);
            return View(araclar.ToList());
        }

        // GET: Arac/AracEkle
        public ActionResult AracEkle()
        {
            try
            {
                ViewBag.ID = new SelectList(db.Yakitlar, "ID", "YakitinAlindigiYer");
                // Boş model gönder ki View'de null reference hatası olmasın
                return View(new Araclar());
            }
            catch (Exception ex)
            {
                TempData["AracEkleHata"] = "Sayfa yüklenirken bir hata olu<PERSON>tu: " + ex.Message;
                return RedirectToAction("AracAnasayfa");
            }
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult AracEkle(Araclar araclar)
        {


            if (araclar.VizeBitisTarihi.HasValue)
                araclar.VizeBitisTarihi = araclar.VizeBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (araclar.SigortaBitisTarihi.HasValue)
                araclar.SigortaBitisTarihi = araclar.SigortaBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (araclar.EgzozBitisTarihi.HasValue)
                araclar.EgzozBitisTarihi = araclar.EgzozBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));

            araclar.KayitTarihi = DateTime.Now;
            araclar.Aktif = true;

            if (ModelState.IsValid)
            {
                db.Araclar.Add(araclar);
                int sonuc = db.SaveChanges();

                if (sonuc>0)
                {
                    TempData["AracEkleBasarili"] = "Araç Ekleme İşlemi Başarılı.";
                }
                else
                {
                    TempData["AracEkleBasarisiz"] = "Araç Ekleme İşlemi Başarısız.";
                }

                return RedirectToAction("AracAnasayfa");
            }

            ViewBag.ID = new SelectList(db.Yakitlar, "ID", "YakitinAlindigiYer", araclar.ID);
            return View(araclar);
        }

        // GET: Arac/Edit/5
        public ActionResult AracDuzenle(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Araclar araclar = db.Araclar.Find(id);
            if (araclar == null)
            {
                return HttpNotFound();
            }
            ViewBag.ID = new SelectList(db.Yakitlar, "ID", "YakitinAlindigiYer", araclar.ID);
            return View(araclar);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult AracDuzenle(Araclar araclar)
        {
            if (araclar.VizeBitisTarihi.HasValue)
                araclar.VizeBitisTarihi = araclar.VizeBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (araclar.SigortaBitisTarihi.HasValue)
                araclar.SigortaBitisTarihi = araclar.SigortaBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (araclar.EgzozBitisTarihi.HasValue)
                araclar.EgzozBitisTarihi = araclar.EgzozBitisTarihi.Value.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));

            if (ModelState.IsValid)
            {
                db.Entry(araclar).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("AracAnasayfa");
            }
            ViewBag.ID = new SelectList(db.Yakitlar, "ID", "YakitinAlindigiYer", araclar.ID);
            return View(araclar);
        }

        // GET: Arac/Delete/5
        public ActionResult AracSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            Araclar araclar = db.Araclar.Find(id);
            if (araclar == null)
            {
                return HttpNotFound();
            }
            return View(araclar);
        }

        // POST: Arac/Delete/5
        [HttpPost, ActionName("AracSil")]
        [ValidateAntiForgeryToken]
        public ActionResult AracSilOnayli(int id)
        {
            Araclar araclar = db.Araclar.Find(id);
            db.Araclar.Remove(araclar);
            db.SaveChanges();
            return RedirectToAction("AracAnasayfa");
        }



        // GET: AlinanYakitlars
        public ActionResult YakitAnasayfa()
        {
            var yakitlar = db.Yakitlar.Include(a => a.Arac);
            return View(yakitlar.ToList());
        }

        // GET: AlinanYakitlar/Ekle
        public ActionResult YakitEkle()
        {
            try
            {
                var araclar = db.Araclar.Where(a => a.Aktif).ToList();
                ViewBag.AracID = new SelectList(araclar, "ID", "Plaka");
                ViewBag.PageHeader = "Yakıt Kaydı Ekle";
                // Boş model gönder ki View'de null reference hatası olmasın
                return View(new AlinanYakitlar());
            }
            catch (Exception ex)
            {
                TempData["YakitHata"] = "Sayfa yüklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("YakitAnasayfa");
            }
        }

        // POST: AlinanYakitlar/Ekle
        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult YakitEkle(AlinanYakitlar alinanYakitlar)
        {
            try
            {
                // Tarih ayarlama
                alinanYakitlar.AlisTarihi = alinanYakitlar.AlisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
                alinanYakitlar.KayitTarihi = DateTime.Now;

                // Litre başına fiyat hesaplama
                if (alinanYakitlar.Litre > 0)
                {
                    alinanYakitlar.LitreBasinaFiyat = alinanYakitlar.YakitTutari / alinanYakitlar.Litre;
                }

                ModelState.Remove("AlisTarihi");
                ModelState.Remove("KayitTarihi");
                ModelState.Remove("LitreBasinaFiyat");

                if (ModelState.IsValid)
                {
                    db.Yakitlar.Add(alinanYakitlar);
                    db.SaveChanges();

                    TempData["YakitBasarili"] = "Yakıt kaydı başarıyla eklendi.";
                    return RedirectToAction("YakitAnasayfa");
                }

                // Hata durumunda dropdown'ı yeniden yükle
                var araclar = db.Araclar.Where(a => a.Aktif).ToList();
                ViewBag.AracID = new SelectList(araclar, "ID", "Plaka", alinanYakitlar.AracID);
                ViewBag.PageHeader = "Yakıt Kaydı Ekle";
                return View(alinanYakitlar);
            }
            catch (Exception ex)
            {
                TempData["YakitHata"] = "Yakıt kaydı eklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("YakitAnasayfa");
            }
        }

        // GET: AlinanYakitlars/Edit/5
        public ActionResult YakitDuzenle(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            AlinanYakitlar alinanYakitlar = db.Yakitlar.Find(id);
            if (alinanYakitlar == null)
            {
                return HttpNotFound();
            }
            ViewBag.ID = new SelectList(db.Araclar, "ID", "Plaka", alinanYakitlar.ID);
            return View(alinanYakitlar);
        }


        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult YakitDuzenle(AlinanYakitlar alinanYakitlar)
        {
            alinanYakitlar.AlisTarihi = alinanYakitlar.AlisTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            ModelState.Remove("AlisTarihi");
            if (ModelState.IsValid)
            {
                db.Entry(alinanYakitlar).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("YakitAnasayfa");
            }
            ViewBag.ID = new SelectList(db.Araclar, "ID", "Plaka", alinanYakitlar.ID);
            return View(alinanYakitlar);
        }

        // GET: AlinanYakitlars/Delete/5
        public ActionResult YakitSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            AlinanYakitlar alinanYakitlar = db.Yakitlar.Find(id);
            if (alinanYakitlar == null)
            {
                return HttpNotFound();
            }
            return View(alinanYakitlar);
        }

        // POST: Arac/YakitSil/5

        [HttpPost, ActionName("YakitSil")]
        [ValidateAntiForgeryToken]
        public ActionResult YakitSilOnayli(int id)
        {
            try
            {
                AlinanYakitlar alinanYakitlar = db.Yakitlar.Find(id);
                if (alinanYakitlar == null)
                {
                    TempData["YakitSilHata"] = "Silinmek istenen yakıt kaydı bulunamadı.";
                    return RedirectToAction("YakitAnasayfa");
                }

                db.Yakitlar.Remove(alinanYakitlar);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["YakitSilBasarili"] = "Yakıt kaydı başarıyla silindi.";
                }
                else
                {
                    TempData["YakitSilHata"] = "Yakıt silme işlemi başarısız oldu.";
                }
            }
            catch (Exception ex)
            {
                TempData["YakitSilHata"] = "Yakıt silme işlemi sırasında bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction("YakitAnasayfa");
        }
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
