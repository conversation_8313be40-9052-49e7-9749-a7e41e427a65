@model IEnumerable<KobiPanel.Models.Kullanicilar>

@{
    ViewBag.Title = "Kullanıcı Yönetimi";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Başarı/Hata Mesajları -->
@if (TempData["KullaniciBasarili"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <strong>Başarılı!</strong> @TempData["KullaniciBasarili"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

@if (TempData["KullaniciHata"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <strong>Hata!</strong> @TempData["KullaniciHata"]
        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
            <span aria-hidden="true">&times;</span>
        </button>
    </div>
}

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-users-cog"></i> Kullanıcı Listesi
                </h5>
                <a href="@Url.Action("Create", "Kullanici")" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Yeni Kullanıcı Ekle
                </a>
            </div>
            <div class="card-body">
                @if (Model != null && Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="thead-dark">
                                <tr>
                                    <th><i class="fas fa-user"></i> Ad Soyad</th>
                                    <th><i class="fas fa-at"></i> Kullanıcı Adı</th>
                                    <th><i class="fas fa-envelope"></i> E-posta</th>
                                    <th><i class="fas fa-phone"></i> Telefon</th>
                                    <th><i class="fas fa-user-tag"></i> Rol</th>
                                    <th><i class="fas fa-toggle-on"></i> Durum</th>
                                    <th><i class="fas fa-calendar"></i> Kayıt Tarihi</th>
                                    <th><i class="fas fa-clock"></i> Son Giriş</th>
                                    <th><i class="fas fa-cogs"></i> İşlemler</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            <strong>@Html.DisplayFor(modelItem => item.AdSoyad)</strong>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">@Html.DisplayFor(modelItem => item.KullaniciAdi)</span>
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Email))
                                            {
                                                <a href="mailto:@item.Email">@Html.DisplayFor(modelItem => item.Email)</a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.Telefon))
                                            {
                                                <a href="tel:@item.Telefon">@Html.DisplayFor(modelItem => item.Telefon)</a>
                                            }
                                            else
                                            {
                                                <span class="text-muted">-</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.Rol == "Yonetici")
                                            {
                                                <span class="badge badge-danger"><i class="fas fa-crown"></i> Yönetici</span>
                                            }
                                            else if (item.Rol == "Kullanici")
                                            {
                                                <span class="badge badge-primary"><i class="fas fa-user"></i> Kullanıcı</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-secondary"><i class="fas fa-eye"></i> Misafir</span>
                                            }
                                        </td>
                                        <td>
                                            @if (item.Aktif)
                                            {
                                                <span class="badge badge-success"><i class="fas fa-check"></i> Aktif</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-warning"><i class="fas fa-times"></i> Pasif</span>
                                            }
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                @if (item.KayitTarihi != default(DateTime))
                                                {
                                                    @item.KayitTarihi.ToString("dd.MM.yyyy")
                                                }
                                                else
                                                {
                                                    <span>-</span>
                                                }
                                            </small>
                                        </td>
                                        <td>
                                            <small class="text-muted">
                                                @if (item.SonGirisTarihi.HasValue)
                                                {
                                                    @item.SonGirisTarihi.Value.ToString("dd.MM.yyyy HH:mm")
                                                }
                                                else
                                                {
                                                    <span>Hiç giriş yapmamış</span>
                                                }
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="@Url.Action("Details", new { id = item.Id })" 
                                                   class="btn btn-sm btn-info" title="Detaylar">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="@Url.Action("Edit", new { id = item.Id })" 
                                                   class="btn btn-sm btn-warning" title="Düzenle">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="@Url.Action("ChangePassword", new { id = item.Id })" 
                                                   class="btn btn-sm btn-secondary" title="Şifre Değiştir">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                                <a href="@Url.Action("Delete", new { id = item.Id })" 
                                                   class="btn btn-sm btn-danger" title="Sil"
                                                   onclick="return confirm('Bu kullanıcıyı silmek istediğinizden emin misiniz?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">Henüz kullanıcı bulunmuyor</h5>
                        <p class="text-muted">İlk kullanıcıyı eklemek için yukarıdaki butonu kullanın.</p>
                        <a href="@Url.Action("Create", "Kullanici")" class="btn btn-primary">
                            <i class="fas fa-plus"></i> İlk Kullanıcıyı Ekle
                        </a>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- İstatistikler -->
@if (Model != null && Model.Any())
{
    <div class="row mt-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.Count()</h4>
                            <p class="mb-0">Toplam Kullanıcı</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.Count(u => u.Aktif)</h4>
                            <p class="mb-0">Aktif Kullanıcı</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.Count(u => u.Rol == "Yonetici")</h4>
                            <p class="mb-0">Yönetici</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-crown fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>@Model.Count(u => u.SonGirisTarihi.HasValue && u.SonGirisTarihi.Value >= DateTime.Now.AddDays(-7))</h4>
                            <p class="mb-0">Son 7 Günde Aktif</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<script>
$(document).ready(function() {
    // DataTable initialization
    if ($('table').length > 0) {
        $('table').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Turkish.json"
            },
            "pageLength": 25,
            "order": [[ 6, "desc" ]], // Kayıt tarihine göre sırala
            "columnDefs": [
                { "orderable": false, "targets": 8 } // İşlemler kolonu sıralanamaz
            ]
        });
    }
    
    // Auto-hide alerts after 5 seconds
    setTimeout(function() {
        $('.alert').fadeOut('slow');
    }, 5000);
});
</script>
