﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Data.Entity;
using System.Linq;
using System.Net;
using System.Web;
using System.Web.Mvc;
using KobiPanel.Helpers;
using KobiPanel.Models;
using KobiPanel.Models.Bicme;


namespace KobiPanel.Controllers
{

    [IPAutorize]
    //TODO: Biçme Viewlarını ve actionlarını optimize edilecek.
    public class BicmeController : Controller
    {
        private KobiPanelDbContext db = new KobiPanelDbContext();

        // GET: Bicme
        public ActionResult TarlaAnasayfa()
        {
            var model = db.BicilenTarlalar.Include(b => b.<PERSON>).ToList();
            return View(model);
        }

        // GET: Bicme/Details/5
        public ActionResult TarlaDetay(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Create
        public ActionResult TarlaEkle()
        {

            ViewBag.SListMusteriler = new SelectList(db.Musteriler, "ID", "adsoyad");
            return View();
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaEkle(BicilenTarlalar bicilenTarlalar)
        {
            bicilenTarlalar.BicimTarihi = bicilenTarlalar.BicimTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));

            bicilenTarlalar.ToplamTutar = (bicilenTarlalar.Donum * bicilenTarlalar.BicmeFiyati);

            bicilenTarlalar.KalanTutar = bicilenTarlalar.ToplamTutar - bicilenTarlalar.TahsilatTutari;

            if (ModelState.IsValid)
            {
                db.BicilenTarlalar.Add(bicilenTarlalar);
                db.SaveChanges();
                return RedirectToAction("TarlaAnasayfa");
            }

            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Edit/5
        public ActionResult TarlaDuzenle(int? id)
        {

            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaDuzenle(BicilenTarlalar bicilenTarlalar)
        {
            bicilenTarlalar.BicimTarihi = bicilenTarlalar.BicimTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));
            if (ModelState.IsValid)
            {
                db.Entry(bicilenTarlalar).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("TarlaAnasayfa");
            }
            ViewBag.MusteriID = new SelectList(db.Musteriler, "ID", "adsoyad", bicilenTarlalar.MusteriID);
            return View(bicilenTarlalar);
        }

        // GET: Bicme/Delete/5
        public ActionResult TarlaSil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
            if (bicilenTarlalar == null)
            {
                return HttpNotFound();
            }
            return View(bicilenTarlalar);
        }

        // POST: Bicme/Delete/5
        [HttpPost, ActionName("TarlaSil")]
        [ValidateAntiForgeryToken]
        public ActionResult TarlaSilOnayli(int id)
        {
            try
            {
                BicilenTarlalar bicilenTarlalar = db.BicilenTarlalar.Find(id);
                if (bicilenTarlalar == null)
                {
                    TempData["TarlaSilHata"] = "Silinmek istenen tarla bulunamadı.";
                    return RedirectToAction("TarlaAnasayfa");
                }

                string tarlaAdi = bicilenTarlalar.TarlaAdi ?? "Bilinmeyen Tarla";
                db.BicilenTarlalar.Remove(bicilenTarlalar);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["TarlaSilBasarili"] = tarlaAdi + " tarlası başarıyla silindi.";
                }
                else
                {
                    TempData["TarlaSilHata"] = "Tarla silme işlemi başarısız oldu.";
                }
            }
            catch (Exception ex)
            {
                TempData["TarlaSilHata"] = "Tarla silme işlemi sırasında bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction("TarlaAnasayfa");
        }

        public ActionResult KantarFisiAnasayfa()
        {
            try
            {
                var kantarFisleri = db.KantarFisi
                    .Include(k => k.BicilenTarlalar)
                    .Include(k => k.BicilenTarlalar.Musteri)
                    .OrderByDescending(k => k.TartimTarihi)
                    .ToList();

                ViewBag.PageHeader = "Kantar Fişi Listesi";
                return View(kantarFisleri);
            }
            catch (Exception ex)
            {
                TempData["KantarFisiHata"] = "Kantar fişleri yüklenirken bir hata oluştu: " + ex.Message;
                return View(new List<KantarFisi>());
            }
        }

        public ActionResult KantarFisiDetay(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            return View(kantarFisi);
        }

        public ActionResult KantarFisiEkle(int? id)
        {

           List<SelectListItem> tarlalar = new List<SelectListItem>();

           foreach (var item in db.BicilenTarlalar.Include(t => t.Musteri).Include(t => t.Musteri.Town).Include(t => t.Musteri.Neighborhood).ToList())
           {
               try
               {
                   string musteriAdi = item.Musteri?.adsoyad ?? "Bilinmeyen Müşteri";
                   string townName = item.Musteri?.Town?.TownName ?? "Bilinmeyen İlçe";
                   string neighborhoodName = item.Musteri?.Neighborhood?.NeighborhoodName ?? "Bilinmeyen Mahalle";
                   string tarlaAdi = item.TarlaAdi ?? "Bilinmeyen Tarla";

                   string displayText = $"{musteriAdi} ({townName}) {neighborhoodName} Tarla Adı: {tarlaAdi}";

                   tarlalar.Add(new SelectListItem {
                       Text = displayText,
                       Value = item.TarlaID.ToString(),
                       Selected = (item.TarlaID == id),
                   });
               }
               catch (Exception ex)
               {
                   // Hatalı kayıt varsa atla
                   System.Diagnostics.Debug.WriteLine("Tarla listesi oluşturma hatası: " + ex.Message);
                   continue;
               }
           }
           ViewBag.TarlaID = tarlalar;
           return View();

        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiEkle(KantarFisi kantarFisi)
        {
            try
            {
                if (ModelState.IsValid)
                {
                    // Net KG hesaplama
                    kantarFisi.NetKg = Math.Abs(kantarFisi.birinciTartim - kantarFisi.ikinciTartim);

                    // Tarih ayarlama
                    kantarFisi.TartimTarihi = kantarFisi.TartimTarihi.Date.Add(new TimeSpan(DateTime.Now.Hour, DateTime.Now.Minute, DateTime.Now.Second));

                    // Müşteri ID'sini tarla bilgisinden al
                    var tarla = db.BicilenTarlalar.Find(kantarFisi.TarlaID);
                    if (tarla != null)
                    {
                        kantarFisi.MusteriID = tarla.MusteriID;
                    }

                    db.KantarFisi.Add(kantarFisi);
                    db.SaveChanges();

                    TempData["KantarFisiBasarili"] = "Kantar fişi başarıyla eklendi.";
                    return RedirectToAction("KantarFisiAnasayfa");
                }

                // Hata durumunda dropdown'ları yeniden yükle
                List<SelectListItem> tarlalar = new List<SelectListItem>();
                foreach (var item in db.BicilenTarlalar.Include(t => t.Musteri).Include(t => t.Musteri.Town).Include(t => t.Musteri.Neighborhood).ToList())
                {
                    try
                    {
                        string musteriAdi = item.Musteri?.adsoyad ?? "Bilinmeyen Müşteri";
                        string townName = item.Musteri?.Town?.TownName ?? "Bilinmeyen İlçe";
                        string neighborhoodName = item.Musteri?.Neighborhood?.NeighborhoodName ?? "Bilinmeyen Mahalle";
                        string tarlaAdi = item.TarlaAdi ?? "Bilinmeyen Tarla";

                        string displayText = $"{musteriAdi} ({townName}) {neighborhoodName} Tarla Adı: {tarlaAdi}";

                        tarlalar.Add(new SelectListItem
                        {
                            Text = displayText,
                            Value = item.TarlaID.ToString(),
                            Selected = (item.TarlaID == kantarFisi.TarlaID),
                        });
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine("Tarla listesi oluşturma hatası: " + ex.Message);
                        continue;
                    }
                }
                ViewBag.TarlaID = tarlalar;
                return View(kantarFisi);
            }
            catch (Exception ex)
            {
                TempData["KantarFisiHata"] = "Kantar fişi eklenirken bir hata oluştu: " + ex.Message;
                return RedirectToAction("KantarFisiAnasayfa");
            }
        }

        public ActionResult KantarFisiDuzenle(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            ViewBag.TarlaID = new SelectList(db.BicilenTarlalar, "TarlaID", "aciklama", kantarFisi.TarlaID);
            return View(kantarFisi);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiDuzenle(KantarFisi kantarFisi)
        {
            if (ModelState.IsValid)
            {
                db.Entry(kantarFisi).State = EntityState.Modified;
                db.SaveChanges();
                return RedirectToAction("KantarFisiAnasayfa");
            }
            ViewBag.TarlaID = new SelectList(db.BicilenTarlalar, "TarlaID", "aciklama", kantarFisi.TarlaID);
            return View(kantarFisi);
        }

        public ActionResult KantarFisiil(int? id)
        {
            if (id == null)
            {
                return new HttpStatusCodeResult(HttpStatusCode.BadRequest);
            }
            KantarFisi kantarFisi = db.KantarFisi.Find(id);
            if (kantarFisi == null)
            {
                return HttpNotFound();
            }
            return View(kantarFisi);
        }

        [HttpPost, ActionName("KantarFisiil")]
        [ValidateAntiForgeryToken]
        public ActionResult KantarFisiilOnayli(int id)
        {
            try
            {
                KantarFisi kantarFisi = db.KantarFisi.Find(id);
                if (kantarFisi == null)
                {
                    TempData["KantarFisiSilHata"] = "Silinmek istenen kantar fişi bulunamadı.";
                    return RedirectToAction("KantarFisiAnasayfa");
                }

                db.KantarFisi.Remove(kantarFisi);
                int sonuc = db.SaveChanges();

                if (sonuc > 0)
                {
                    TempData["KantarFisiSilBasarili"] = "Kantar fişi başarıyla silindi.";
                }
                else
                {
                    TempData["KantarFisiSilHata"] = "Kantar fişi silme işlemi başarısız oldu.";
                }
            }
            catch (Exception ex)
            {
                TempData["KantarFisiSilHata"] = "Kantar fişi silme işlemi sırasında bir hata oluştu: " + ex.Message;
            }

            return RedirectToAction("KantarFisiAnasayfa");
        }

        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                db.Dispose();
            }
            base.Dispose(disposing);
        }
    }
}
