@{
    ViewBag.Title = "Hata Oluştu";
    Layout = "~/Views/Shared/_Layout.cshtml";
    
    string errorMessage = ViewData["ErrorMessage"]?.ToString() ?? "Beklenmedik bir hata oluştu.";
    string errorSeverity = ViewData["ErrorSeverity"]?.ToString() ?? "Low";
    bool showDetails = (bool)(ViewData["ShowDetails"] ?? false);
    string errorDetails = ViewData["ErrorDetails"]?.ToString();
    string errorStackTrace = ViewData["ErrorStackTrace"]?.ToString();
}

<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow">
                <div class="card-header @GetSeverityClass(errorSeverity) text-white">
                    <h4 class="mb-0">
                        <i class="fas @GetSeverityIcon(errorSeverity) me-2"></i>
                        @GetSeverityTitle(errorSeverity)
                    </h4>
                </div>
                <div class="card-body">
                    <div class="alert @GetAlertClass(errorSeverity)" role="alert">
                        <h5 class="alert-heading">@errorMessage</h5>
                        <hr>
                        <p class="mb-0">
                            <small class="text-muted">
                                <i class="fas fa-clock me-1"></i>
                                @DateTime.Now.ToString("dd.MM.yyyy HH:mm:ss")
                            </small>
                        </p>
                    </div>

                    @if (showDetails && !string.IsNullOrEmpty(errorDetails))
                    {
                        <div class="mt-4">
                            <div class="card bg-light">
                                <div class="card-header">
                                    <h6 class="mb-0">
                                        <i class="fas fa-bug me-2"></i>
                                        Teknik Detaylar (Development Mode)
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <strong>Hata Mesajı:</strong>
                                        <pre class="bg-white p-2 border rounded"><code>@errorDetails</code></pre>
                                    </div>
                                    
                                    @if (!string.IsNullOrEmpty(errorStackTrace))
                                    {
                                        <div class="mb-3">
                                            <strong>Stack Trace:</strong>
                                            <div class="accordion" id="stackTraceAccordion">
                                                <div class="accordion-item">
                                                    <h2 class="accordion-header" id="stackTraceHeading">
                                                        <button class="accordion-button collapsed" type="button" 
                                                                data-bs-toggle="collapse" data-bs-target="#stackTraceCollapse">
                                                            Stack Trace'i Göster
                                                        </button>
                                                    </h2>
                                                    <div id="stackTraceCollapse" class="accordion-collapse collapse" 
                                                         data-bs-parent="#stackTraceAccordion">
                                                        <div class="accordion-body">
                                                            <pre class="bg-white p-2 border rounded" style="font-size: 12px; max-height: 300px; overflow-y: auto;"><code>@errorStackTrace</code></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    }

                    <div class="mt-4 text-center">
                        <a href="@Url.Action("Index", "Home")" class="btn btn-primary me-2">
                            <i class="fas fa-home me-1"></i>
                            Ana Sayfaya Dön
                        </a>
                        <button onclick="history.back()" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i>
                            Geri Git
                        </button>
                        <button onclick="location.reload()" class="btn btn-outline-primary">
                            <i class="fas fa-redo me-1"></i>
                            Sayfayı Yenile
                        </button>
                    </div>

                    @if (errorSeverity == "Critical" || errorSeverity == "High")
                    {
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>Bilgilendirme</h6>
                                <p class="mb-0">
                                    Bu hata otomatik olarak sistem yöneticilerine bildirilmiştir. 
                                    Sorun devam ederse lütfen teknik destek ile iletişime geçin.
                                </p>
                            </div>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@functions {
    string GetSeverityClass(string severity)
    {
        return severity switch
        {
            "Critical" => "bg-danger",
            "High" => "bg-warning",
            "Medium" => "bg-info",
            _ => "bg-secondary"
        };
    }

    string GetAlertClass(string severity)
    {
        return severity switch
        {
            "Critical" => "alert-danger",
            "High" => "alert-warning",
            "Medium" => "alert-info",
            _ => "alert-secondary"
        };
    }

    string GetSeverityIcon(string severity)
    {
        return severity switch
        {
            "Critical" => "fa-exclamation-triangle",
            "High" => "fa-exclamation-circle",
            "Medium" => "fa-info-circle",
            _ => "fa-question-circle"
        };
    }

    string GetSeverityTitle(string severity)
    {
        return severity switch
        {
            "Critical" => "Kritik Sistem Hatası",
            "High" => "Önemli Hata",
            "Medium" => "Sistem Uyarısı",
            _ => "Bilgilendirme"
        };
    }
}

<style>
    .card {
        border-radius: 10px;
    }
    
    .card-header {
        border-radius: 10px 10px 0 0 !important;
    }
    
    pre code {
        font-family: 'Courier New', monospace;
        font-size: 12px;
        line-height: 1.4;
    }
    
    .accordion-button:not(.collapsed) {
        background-color: #e9ecef;
        color: #495057;
    }
</style>
