namespace KobiPanel.Models
{
    using System;
    using System.Data.Entity;
    using System.ComponentModel.DataAnnotations.Schema;
    using System.Linq;
    using KobiPanel.Models.Vehicles;
    using KobiPanel.Models.Bicme;
    using AspNetMvcFilters.Models;
    

    public class KobiPanelDbContext : DbContext
    {
        public KobiPanelDbContext()
            : base("name=KobiPanelDbContext")
        {
            Configuration.LazyLoadingEnabled = true;
            Configuration.ProxyCreationEnabled = true;
        }

        // Ana Modeller
        public DbSet<Musteriler> Musteriler { get; set; }
        public DbSet<Satislar> Satislar { get; set; }
        public DbSet<Giderler> Giderler { get; set; }
        public DbSet<Not> Not { get; set; }
        public DbSet<Urun> Urun { get; set; }
        public DbSet<Kullanicilar> Kullanicilar { get; set; }
        public DbSet<Log> Log { get; set; }

        // Adres Modelleri
        public DbSet<City> City { get; set; }
        public DbSet<District> District { get; set; }
        public DbSet<Town> Town { get; set; }
        public DbSet<Neighborhood> Neighborhood { get; set; }

        // Araç Modelleri
        public DbSet<Araclar> Araclar { get; set; }
        public DbSet<AlinanYakitlar> Yakitlar { get; set; }

        // Biçme Modelleri
        public DbSet<BicilenTarlalar> BicilenTarlalar { get; set; }
        public DbSet<KantarFisi> KantarFisi { get; set; }

        // Bildirim Modelleri
        public DbSet<Bildirim> Bildirimler { get; set; }

        protected override void OnModelCreating(DbModelBuilder modelBuilder)
        {
            // Null reference hatalarını önlemek için konfigürasyonlar

            // Müşteri ilişkileri
            modelBuilder.Entity<Musteriler>()
                .HasOptional(m => m.City)
                .WithMany()
                .HasForeignKey(m => m.CityID)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Musteriler>()
                .HasOptional(m => m.Town)
                .WithMany()
                .HasForeignKey(m => m.TownID)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<Musteriler>()
                .HasOptional(m => m.Neighborhood)
                .WithMany()
                .HasForeignKey(m => m.NeighborhoodID)
                .WillCascadeOnDelete(false);

            // Biçme ilişkileri
            modelBuilder.Entity<BicilenTarlalar>()
                .HasRequired(b => b.Musteri)
                .WithMany()
                .HasForeignKey(b => b.MusteriID)
                .WillCascadeOnDelete(false);

            modelBuilder.Entity<KantarFisi>()
                .HasRequired(k => k.BicilenTarlalar)
                .WithMany(b => b.kantarfisi)
                .HasForeignKey(k => k.TarlaID)
                .WillCascadeOnDelete(false);

            // Araç ilişkileri
            modelBuilder.Entity<AlinanYakitlar>()
                .HasRequired(y => y.Arac)
                .WithMany(a => a.Yakitlar)
                .HasForeignKey(y => y.AracID)
                .WillCascadeOnDelete(false);

            // Bildirim ilişkileri
            modelBuilder.Entity<Bildirim>()
                .HasOptional(b => b.Kullanici)
                .WithMany()
                .HasForeignKey(b => b.KullaniciId)
                .WillCascadeOnDelete(false);

            base.OnModelCreating(modelBuilder);
        }
    }
}
